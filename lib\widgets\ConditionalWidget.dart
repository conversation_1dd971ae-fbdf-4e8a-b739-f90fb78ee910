import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:message_divert_3/widgets/FormWidget.dart';
import 'package:message_divert_3/widgets/NoPermissionWidget.dart';
import 'package:permission_handler/permission_handler.dart';

class Conditionalwidget extends StatefulWidget {
  const Conditionalwidget({
    super.key,
    required this.onSend,
    required this.controller,
  });
  final VoidCallback onSend;
  final TextEditingController controller;
  @override
  State<Conditionalwidget> createState() => _ConditionalwidgetState();
}

class _ConditionalwidgetState extends State<Conditionalwidget> {
  FirebaseMessaging messaging = FirebaseMessaging.instance;
  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: Future.wait([
        messaging.requestPermission(
          alert: true,
          announcement: false,
          badge: true,
          carPlay: false,
          criticalAlert: false,
          provisional: false,
          sound: true,
        ),
        Permission.sms.request(),
      ]),
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.hasData) {
          NotificationSettings notificationSettings = snapshot.data[0];
          PermissionStatus smsStatus = snapshot.data[1];

          if (notificationSettings.authorizationStatus !=
                  AuthorizationStatus.authorized ||
              smsStatus != PermissionStatus.granted) {
            return Nopermissionwidget();
          }
          return Formwidget(
            onSend: widget.onSend,
            controller: widget.controller,
          );
        }
        return Nopermissionwidget();
      },
    );
  }
}
