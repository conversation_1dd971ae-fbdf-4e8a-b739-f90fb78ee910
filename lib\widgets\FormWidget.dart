import 'package:flutter/material.dart';

class Formwidget extends StatefulWidget {
  const Formwidget({super.key, required this.onSend, required this.controller});
  final VoidCallback onSend;
  final TextEditingController controller;
  @override
  State<Formwidget> createState() => _FormwidgetState();
}

class _FormwidgetState extends State<Formwidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        TextField(
          controller: widget.controller,
          textAlign: TextAlign.right,
          textDirection: TextDirection.rtl,
          decoration: InputDecoration(
            border: OutlineInputBorder(),
            hintText: "شماره تماس",
          ),
        ),
        Sized<PERSON>ox(height: 20),
        ElevatedButton(onPressed: widget.onSend, child: Text("ارسال")),
      ],
    );
  }
}
