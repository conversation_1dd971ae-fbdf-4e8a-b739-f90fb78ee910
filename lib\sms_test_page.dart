import 'package:flutter/material.dart';
import 'package:message_divert_3/sms_service.dart';

class SmsTestPage extends StatefulWidget {
  const SmsTestPage({super.key});

  @override
  State<SmsTestPage> createState() => _SmsTestPageState();
}

class _SmsTestPageState extends State<SmsTestPage> {
  final _phoneController = TextEditingController();
  final _messageController = TextEditingController();
  final List<SmsStatusTracker> _smsHistory = [];
  bool _isLoading = false;
  String _statusMessage = '';

  @override
  void dispose() {
    _phoneController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  void _updateStatus(String message) {
    setState(() {
      _statusMessage = message;
    });
  }

  Future<void> _sendSms() async {
    if (_phoneController.text.isEmpty || _messageController.text.isEmpty) {
      _updateStatus('❌ Please enter both phone number and message');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = 'Sending SMS...';
    });

    final tracker = SmsStatusTracker(
      phoneNumber: _phoneController.text,
      message: _messageController.text,
      timestamp: DateTime.now(),
    );

    setState(() {
      _smsHistory.insert(0, tracker);
    });

    try {
      await SmsService.sendSmsWithStatus(
        phoneNumber: _phoneController.text,
        message: _messageController.text,
        onSent: (status) {
          tracker.updateStatus(SmsDeliveryStatus.sent);
          _updateStatus('✅ SMS sent successfully!');
          setState(() {});
        },
        onDelivered: (status) {
          tracker.updateStatus(SmsDeliveryStatus.delivered);
          _updateStatus('📱 SMS delivered successfully!');
          setState(() {});
        },
        onError: (error) {
          tracker.updateStatus(SmsDeliveryStatus.failed, error);
          _updateStatus('❌ Error: $error');
          setState(() {});
        },
      );
    } catch (e) {
      tracker.updateStatus(SmsDeliveryStatus.failed, e.toString());
      _updateStatus('❌ Failed to send SMS: $e');
      setState(() {});
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _sendViaDefaultApp() async {
    if (_phoneController.text.isEmpty || _messageController.text.isEmpty) {
      _updateStatus('❌ Please enter both phone number and message');
      return;
    }

    try {
      await SmsService.sendSmsViaDefaultApp(
        phoneNumber: _phoneController.text,
        message: _messageController.text,
      );
      _updateStatus('📱 Opened default SMS app');
    } catch (e) {
      _updateStatus('❌ Error opening SMS app: $e');
    }
  }

  Future<void> _checkPermissions() async {
    final hasPermission = await SmsService.requestSmsPermissions();
    final canSend = await SmsService.canSendSms();
    
    _updateStatus(
      'Permissions: ${hasPermission ? '✅' : '❌'} | '
      'Can Send SMS: ${canSend ? '✅' : '❌'}'
    );
  }

  Widget _buildStatusIcon(SmsDeliveryStatus status) {
    switch (status) {
      case SmsDeliveryStatus.pending:
        return const Icon(Icons.schedule, color: Colors.orange);
      case SmsDeliveryStatus.sent:
        return const Icon(Icons.send, color: Colors.blue);
      case SmsDeliveryStatus.delivered:
        return const Icon(Icons.check_circle, color: Colors.green);
      case SmsDeliveryStatus.failed:
        return const Icon(Icons.error, color: Colors.red);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SMS Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                hintText: '+1234567890',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                labelText: 'Message',
                hintText: 'Enter your message here',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _sendSms,
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Send SMS'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _sendViaDefaultApp,
                    child: const Text('Via SMS App'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _checkPermissions,
              child: const Text('Check Permissions'),
            ),
            const SizedBox(height: 16),
            if (_statusMessage.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _statusMessage,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            const SizedBox(height: 16),
            const Text(
              'SMS History:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: _smsHistory.length,
                itemBuilder: (context, index) {
                  final sms = _smsHistory[index];
                  return Card(
                    child: ListTile(
                      leading: _buildStatusIcon(sms.status),
                      title: Text(sms.phoneNumber),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(sms.message),
                          Text(
                            '${sms.timestamp.toString().substring(0, 19)} - ${sms.status.name}',
                            style: const TextStyle(fontSize: 12),
                          ),
                          if (sms.errorMessage != null)
                            Text(
                              'Error: ${sms.errorMessage}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.red,
                              ),
                            ),
                        ],
                      ),
                      isThreeLine: true,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
