import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:http/http.dart' as http;

import 'package:flutter/material.dart';
import 'package:message_divert_3/widgets/ConditionalWidget.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  TextEditingController controller = TextEditingController();
  late SharedPreferences preferences;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      preferences = await SharedPreferences.getInstance();
      String? phone = preferences.getString("phone");
      controller.text = phone ?? "";
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Conditionalwidget(
              controller: controller,
              onSend: _handleSendButton,
            ),
          ),
        ),
      ),
    );
  }

  void _handleSendButton() async {
    if (controller.text.length != 11 || !controller.text.startsWith("09")) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text("شماره وار شده معتبر نیست ")));
      return;
    }
    preferences.setString('phone', controller.text);
    var url = Uri.http('185.110.191.49:8534', 'api/users');
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    String? token = await messaging.getToken();
    try {
      var response = await http.post(
        url,
        headers: {
          "Content-Type": "application/json",
          'Application-Token': "MYAPP",
        },
        body: jsonEncode({"phone": controller.text, "notif_token": token}),
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text("با موفقیت ارسال شد")));
        print('Data sent successfully');
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text("مشکل 2")));
        print('Server error: ${response.statusCode}');
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text("مشکل")));
      print('Request failed: $e');
    }
  }
}
