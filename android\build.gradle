allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

// Fix for plugins that don't have namespace specified
subprojects { subproject ->
    subproject.plugins.withId("com.android.library") {
        subproject.android {
            if (namespace == null) {
                namespace = subproject.group
            }
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
