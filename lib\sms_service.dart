import 'package:telephony/telephony.dart';

class SmsService {
  static final Telephony _telephony = Telephony.instance;

  /// Send SMS with status callback
  static Future<void> sendSmsWithStatus({
    required String phoneNumber,
    required String message,
    Function(String)? onSent,
    Function(String)? onDelivered,
    Function(String)? onError,
  }) async {
    try {
      await _telephony.sendSms(
        to: phoneNumber,
        message: message,
        statusListener: (SendStatus status) {
          switch (status) {
            case SendStatus.SENT:
              print('✅ SMS sent successfully to $phoneNumber');
              onSent?.call('SMS sent successfully to $phoneNumber');
              break;
            case SendStatus.DELIVERED:
              print('📱 SMS delivered successfully to $phoneNumber');
              onDelivered?.call('SMS delivered successfully to $phoneNumber');
              break;
          }
        },
      );
      print('📤 SMS sending initiated - phone: $phoneNumber, message: $message');
    } catch (e) {
      print('❌ Error sending SMS: $e');
      onError?.call('Error sending SMS: $e');
      rethrow;
    }
  }

  /// Send SMS via default SMS app (always successful if app opens)
  static Future<void> sendSmsViaDefaultApp({
    required String phoneNumber,
    required String message,
  }) async {
    try {
      await _telephony.sendSmsByDefaultApp(
        to: phoneNumber,
        message: message,
      );
      print('📱 SMS sent via default app to $phoneNumber');
    } catch (e) {
      print('❌ Error opening default SMS app: $e');
      rethrow;
    }
  }

  /// Send multipart SMS for long messages
  static Future<void> sendLongSms({
    required String phoneNumber,
    required String message,
    Function(String)? onStatusUpdate,
  }) async {
    try {
      await _telephony.sendSms(
        to: phoneNumber,
        message: message,
        isMultipart: true,
        statusListener: (SendStatus status) {
          String statusMessage;
          switch (status) {
            case SendStatus.SENT:
              statusMessage = 'Long SMS sent successfully to $phoneNumber';
              break;
            case SendStatus.DELIVERED:
              statusMessage = 'Long SMS delivered successfully to $phoneNumber';
              break;
          }
          print('📨 $statusMessage');
          onStatusUpdate?.call(statusMessage);
        },
      );
    } catch (e) {
      print('❌ Error sending long SMS: $e');
      rethrow;
    }
  }

  /// Check if device can send SMS
  static Future<bool> canSendSms() async {
    try {
      return await _telephony.isSmsCapable ?? false;
    } catch (e) {
      print('❌ Error checking SMS capability: $e');
      return false;
    }
  }

  /// Request SMS permissions
  static Future<bool> requestSmsPermissions() async {
    try {
      return await _telephony.requestSmsPermissions ?? false;
    } catch (e) {
      print('❌ Error requesting SMS permissions: $e');
      return false;
    }
  }

  /// Get detailed device info for debugging
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      final simState = await _telephony.simState;
      final canSend = await _telephony.isSmsCapable;
      final networkOperator = await _telephony.networkOperator;
      final networkOperatorName = await _telephony.networkOperatorName;
      
      return {
        'canSendSms': canSend,
        'simState': simState.toString(),
        'networkOperator': networkOperator,
        'networkOperatorName': networkOperatorName,
      };
    } catch (e) {
      print('❌ Error getting device info: $e');
      return {'error': e.toString()};
    }
  }
}

/// Enum for tracking SMS status in your app
enum SmsDeliveryStatus {
  pending,
  sent,
  delivered,
  failed,
}

/// Class to track SMS status with additional metadata
class SmsStatusTracker {
  final String phoneNumber;
  final String message;
  final DateTime timestamp;
  SmsDeliveryStatus status;
  String? errorMessage;

  SmsStatusTracker({
    required this.phoneNumber,
    required this.message,
    required this.timestamp,
    this.status = SmsDeliveryStatus.pending,
    this.errorMessage,
  });

  void updateStatus(SmsDeliveryStatus newStatus, [String? error]) {
    status = newStatus;
    errorMessage = error;
    print('📊 SMS Status Update: $phoneNumber -> ${status.name}${error != null ? ' ($error)' : ''}');
  }

  Map<String, dynamic> toJson() {
    return {
      'phoneNumber': phoneNumber,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'errorMessage': errorMessage,
    };
  }
}
