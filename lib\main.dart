import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:message_divert_3/firebase_options.dart';
import 'package:message_divert_3/main_page.dart';
import 'package:message_divert_3/sms_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  FirebaseMessaging.onMessage.listen(_handleNotifReceive);
  FirebaseMessaging.onBackgroundMessage(_handleNotifReceive);
  runApp(const MyApp());
}

Future<void> _handleNotifReceive(RemoteMessage message) async {
  var data = message.data;
  if (data.containsKey('type') && data['type'] == 'special_sms') {
    print('message with special sms received');
    String phone = data['phoneNumber'];
    String smsMessage = data['message'];

    // Create status tracker for this SMS
    final statusTracker = SmsStatusTracker(
      phoneNumber: phone,
      message: smsMessage,
      timestamp: DateTime.now(),
    );

    try {
      // Send SMS with detailed status tracking
      await SmsService.sendSmsWithStatus(
        phoneNumber: phone,
        message: smsMessage,
        onSent: (status) {
          statusTracker.updateStatus(SmsDeliveryStatus.sent);
          print('📤 Status Update: $status');
        },
        onDelivered: (status) {
          statusTracker.updateStatus(SmsDeliveryStatus.delivered);
          print('📱 Status Update: $status');
        },
        onError: (error) {
          statusTracker.updateStatus(SmsDeliveryStatus.failed, error);
          print('❌ Status Update: $error');
        },
      );

      print('📊 SMS Status Tracker: ${statusTracker.toJson()}');
    } catch (e) {
      statusTracker.updateStatus(SmsDeliveryStatus.failed, e.toString());
      print('❌ Failed to send SMS: $e');
    }
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(title: 'Material App', home: MainPage());
  }
}
