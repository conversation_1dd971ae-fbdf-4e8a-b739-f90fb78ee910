import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:message_divert_3/firebase_options.dart';
import 'package:message_divert_3/main_page.dart';
import 'package:telephony/telephony.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  FirebaseMessaging.onMessage.listen(_handleNotifReceive);
  FirebaseMessaging.onBackgroundMessage(_handleNotifReceive);
  runApp(const MyApp());
}

Future<void> _handleNotifReceive(RemoteMessage message) async {
  var data = message.data;
  if (data.containsKey('type') && data['type'] == 'special_sms') {
    print('message with special sms received');
    String phone = data['phoneNumber'];
    String message = data['message'];
    Telephony telephony= Telephony.instance;
    telephony.sendSms(to: phone, message: message);
    print('phone: $phone, message: $message');
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(title: 'Material App', home: MainPage());
  }
}
