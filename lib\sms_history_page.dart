import 'package:flutter/material.dart';
import 'package:message_divert_3/sms_service.dart';

class SmsHistoryPage extends StatefulWidget {
  const SmsHistoryPage({super.key});

  @override
  State<SmsHistoryPage> createState() => _SmsHistoryPageState();
}

class _SmsHistoryPageState extends State<SmsHistoryPage> {
  List<Map<String, dynamic>> _smsHistory = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSmsHistory();
  }

  Future<void> _loadSmsHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final history = await SmsService.getSmsHistory();
      setState(() {
        _smsHistory = history;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading SMS history: $e')),
        );
      }
    }
  }

  Future<void> _clearHistory() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear SMS History'),
        content: const Text('Are you sure you want to clear all SMS history?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Clear'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await SmsService.clearSmsHistory();
      _loadSmsHistory();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('SMS history cleared')),
        );
      }
    }
  }

  String _formatDateTime(String isoString) {
    try {
      final dateTime = DateTime.parse(isoString);
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return isoString;
    }
  }

  Widget _buildStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'sent':
        return const Icon(Icons.send, color: Colors.green, size: 20);
      case 'delivered':
        return const Icon(Icons.check_circle, color: Colors.blue, size: 20);
      case 'failed':
        return const Icon(Icons.error, color: Colors.red, size: 20);
      default:
        return const Icon(Icons.schedule, color: Colors.orange, size: 20);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SMS History'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSmsHistory,
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _smsHistory.isNotEmpty ? _clearHistory : null,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _smsHistory.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.message_outlined,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'No SMS history found',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'SMS sent through this app will appear here',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: _smsHistory.length,
                  itemBuilder: (context, index) {
                    final sms = _smsHistory[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(vertical: 4),
                      child: ListTile(
                        leading: _buildStatusIcon(sms['status'] ?? 'unknown'),
                        title: Text(
                          sms['phoneNumber'] ?? 'Unknown',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              sms['message'] ?? '',
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _formatDateTime(sms['timestamp'] ?? ''),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        isThreeLine: true,
                        trailing: Chip(
                          label: Text(
                            sms['status'] ?? 'unknown',
                            style: const TextStyle(fontSize: 12),
                          ),
                          backgroundColor: sms['status'] == 'sent'
                              ? Colors.green.withOpacity(0.2)
                              : sms['status'] == 'delivered'
                                  ? Colors.blue.withOpacity(0.2)
                                  : Colors.red.withOpacity(0.2),
                        ),
                        onTap: () {
                          // Show full message in dialog
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text('SMS to ${sms['phoneNumber']}'),
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Message:',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(sms['message'] ?? ''),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Sent: ${_formatDateTime(sms['timestamp'] ?? '')}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  Text(
                                    'Status: ${sms['status'] ?? 'unknown'}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: const Text('Close'),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
    );
  }
}
